import React from 'react';
import { motion } from 'motion/react';
import { X } from 'lucide-react';

const SelectedImages = ({ selectedImages, onRemoveImage, onClearAll }) => (
  <motion.div
    className="mb-3 p-3 bg-white rounded-2xl border-2 border-dexin-light-20"
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
  >
    <div className="flex items-center justify-between mb-2">
      <span className="text-sm font-medium text-gray-700">
        Hình ảnh đã chọn ({selectedImages.length})
      </span>
      <button
        onClick={onClearAll}
        className="text-gray-500 hover:text-red-500 transition-colors"
      >
        <X size={16} />
      </button>
    </div>
    <div className="grid grid-cols-3 sm:grid-cols-4 gap-2">
      {selectedImages.map((image, index) => (
        <div key={index} className="relative group">
          <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
            <img
              src={image.preview}
              alt={image.name}
              className="w-full h-full object-cover"
            />
          </div>
          <button
            onClick={() => onRemoveImage(index)}
            className="absolute -top-1 -right-1 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
          >
            <X size={12} />
          </button>
        </div>
      ))}
    </div>
  </motion.div>
);

export default SelectedImages;
