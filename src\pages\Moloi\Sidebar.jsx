import React from 'react';
import { motion } from 'motion/react';
import { ArrowLeft, Search, PenSquare } from 'lucide-react';
import { Link } from 'react-router-dom';
import MessageCounter from './MessageCounter';
import { generateChatSamples } from './utils';
import { ANIMATION_VARIANTS } from './constants';

const Sidebar = ({ 
  showSidebar, 
  toggleSidebar, 
  isLimitReached, 
  messageCount, 
  messageLimit, 
  chatHistory 
}) => {
  return (
    <motion.div
      className="w-full sm:w-64 md:w-72 lg:w-80 flex-shrink-0 bg-dexin-light-20 flex flex-col h-full fixed sm:relative z-50 sm:z-auto"
      initial={{ x: -320 }}
      animate={{ x: 0 }}
      exit={{ x: -320 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      {/* Sidebar header - Responsive */}
      <div className="p-3 sm:p-4 flex items-center justify-between">
        <div className="flex items-center">
          <motion.button
            onClick={toggleSidebar}
            className="mr-2 p-1.5 sm:p-1 rounded-full hover:bg-pink-100 text-gray-600 hover:text-dexin-primary"
            aria-label="Close sidebar"
            whileTap={{ scale: 0.9 }}
          >
            <ArrowLeft size={20} />
          </motion.button>
          <Link to="/" className="flex items-center">
            <motion.img
              src="/images/Logo Mini.png"
              alt="DEXIN"
              className="h-8 sm:h-10"
              whileHover={{ scale: 1.05 }}
            />
          </Link>
        </div>
        <div className="flex items-center space-x-2 sm:space-x-3">
          <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
            <Search size={20} className="sm:w-6 sm:h-6 text-gray-600 cursor-pointer hover:text-dexin-primary" />
          </motion.div>
          <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
            <PenSquare size={20} className="sm:w-6 sm:h-6 text-gray-600 cursor-pointer hover:text-dexin-primary" />
          </motion.div>
        </div>
      </div>

      {/* Message Counter in Sidebar */}
      <div className="px-3 sm:px-4 pb-3">
        <MessageCounter 
          isLimitReached={isLimitReached}
          messageCount={messageCount}
          messageLimit={messageLimit}
          className="w-full text-center"
          isSidebar={true}
        />
      </div>

      {/* Chat history list with staggered animation - Responsive */}
      <motion.div
        className="flex-1 overflow-y-auto px-2 sm:px-2 pt-2"
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.stagger}
      >
        {generateChatSamples(chatHistory).map(chat =>
          chat.isHeader ? (
            <motion.div
              key={chat.id}
              className="px-3 sm:px-4 py-2 text-sm font-medium text-gray-500"
              variants={ANIMATION_VARIANTS.slideIn}
            >
              {chat.title}
            </motion.div>
          ) : (
            <motion.div
              key={chat.id}
              className={`py-3 px-3 sm:px-4 rounded-xl cursor-pointer mb-1 ${
                chat.active ? 'bg-dexin-light-50' : 'hover:bg-pink-50'
              }`}
              variants={ANIMATION_VARIANTS.slideIn}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="text-sm font-medium truncate max-w-full">{chat.title}</div>
            </motion.div>
          )
        )}
      </motion.div>
    </motion.div>
  );
};

export default Sidebar;
