import React from 'react';
import { motion } from 'motion/react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { WELCOME_CONTENT } from './constants';

const WelcomeMessage = () => (
  <div className="flex-1 flex flex-col items-center justify-center px-4 sm:px-6">
    <motion.div
      className="text-center"
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
    >
      <motion.h2
        className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-dexin-primary mb-2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        Chào mừng bạn đến với
      </motion.h2>
      <motion.h2
        className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-dexin-primary mb-6 sm:mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        Nara - Chuyên gia nội thất chữa lành
      </motion.h2>
    </motion.div>
    <motion.div
      className="w-full max-w-2xl px-2 sm:px-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.7 }}
    >
      <div className="p-3 sm:p-4 bg-white rounded-2xl shadow-sm rounded-bl-none">
        <div className="markdown-content prose prose-sm max-w-none text-sm sm:text-base">
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {WELCOME_CONTENT}
          </ReactMarkdown>
        </div>
      </div>
    </motion.div>
  </div>
);

export default WelcomeMessage;
