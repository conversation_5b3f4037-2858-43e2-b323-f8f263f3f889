import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { ArrowRight, ArrowLeft, Search, PenSquare, Menu, ImageIcon, X } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useChat } from '../context/ChatContext';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import ImageUpload from '../components/chat/ImageUpload';

// Constants
const WELCOME_CONTENT = 'Xin chào! Tôi là **Nara**, chuyên gia tư vấn nội thất phong cách **chữa lành** của DEXIN 🌿✨\n\nTôi chuyên thiết kế không gian sống hỗ trợ sức khỏe tinh thần và thể chất của bạn. Hãy để tôi giúp bạn tạo ra một ngôi nhà thực sự là nơi **hồi phục** và **tái tạo năng lượng**!\n\n💫 Tôi có thể tư vấn về:\n- Màu sắc và vật liệu tự nhiên có tác dụng chữa lành\n- Bố trí không gian theo nguyên tắc feng shui\n- Cây xanh và ánh sáng tự nhiên\n- Thiết kế phòng ngủ hỗ trợ giấc ngủ sâu\n- Tạo góc thư giãn, thiền định\n\n📸 **Mới**: Bạn có thể gửi hình ảnh không gian của mình để tôi phân tích và tư vấn cụ thể!\n\nBạn muốn cải thiện không gian nào trong nhà để có cảm giác bình yên và khỏe mạnh hơn? 🏡';

const ANIMATION_VARIANTS = {
  sidebar: {
    hidden: { x: -320 },
    visible: { x: 0 },
    transition: { type: "spring", stiffness: 300, damping: 30 }
  },
  stagger: {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { staggerChildren: 0.07 } }
  },
  slideIn: {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0 }
  }
};

// Helper functions
const getCounterStyle = (isLimitReached, messageCount, messageLimit) => {
  if (isLimitReached) return 'bg-red-100 text-red-600';
  if (messageCount >= messageLimit - 3) return 'bg-yellow-100 text-yellow-600';
  return 'bg-green-100 text-green-600';
};

const getCounterText = (isLimitReached, messageCount, messageLimit, isSidebar = false) => {
  if (isLimitReached) return isSidebar ? '🚫 Hết lượt hỏi hôm nay' : '� Hết lượt hỏi';
  const suffix = isSidebar ? ' lượt hỏi' : ' lượt';
  return `💬 Còn ${messageLimit - messageCount}/${messageLimit}${suffix}`;
};

// Components
const MessageCounter = ({ isLimitReached, messageCount, messageLimit, className = "", isSidebar = false }) => (
  <motion.div
    className={`px-3 py-1 rounded-full text-xs sm:text-sm font-medium ${getCounterStyle(isLimitReached, messageCount, messageLimit)} ${className}`}
    initial={{ opacity: 0, scale: 0.8 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.3 }}
  >
    {getCounterText(isLimitReached, messageCount, messageLimit, isSidebar)}
  </motion.div>
);

const SelectedImages = ({ selectedImages, onRemoveImage, onClearAll }) => (
  <motion.div
    className="mb-3 p-3 bg-white rounded-2xl border-2 border-dexin-light-20"
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
  >
    <div className="flex items-center justify-between mb-2">
      <span className="text-sm font-medium text-gray-700">
        Hình ảnh đã chọn ({selectedImages.length})
      </span>
      <button
        onClick={onClearAll}
        className="text-gray-500 hover:text-red-500 transition-colors"
      >
        <X size={16} />
      </button>
    </div>
    <div className="grid grid-cols-3 sm:grid-cols-4 gap-2">
      {selectedImages.map((image, index) => (
        <div key={index} className="relative group">
          <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
            <img
              src={image.preview}
              alt={image.name}
              className="w-full h-full object-cover"
            />
          </div>
          <button
            onClick={() => onRemoveImage(index)}
            className="absolute -top-1 -right-1 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
          >
            <X size={12} />
          </button>
        </div>
      ))}
    </div>
  </motion.div>
);

const LoadingIndicator = () => (
  <motion.div
    className="mb-4"
    initial={{ opacity: 0, scale: 0.8 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.3 }}
  >
    <div className="p-3 sm:p-4 bg-dexin-light-20 rounded-2xl shadow-sm rounded-bl-none max-w-[90%] sm:max-w-[85%]">
      <div className="flex space-x-2">
        <div className="w-2 h-2 rounded-full bg-white animate-bounce"></div>
        <div className="w-2 h-2 rounded-full bg-white animate-bounce delay-75"></div>
        <div className="w-2 h-2 rounded-full bg-white animate-bounce delay-150"></div>
      </div>
    </div>
  </motion.div>
);

const ErrorMessage = ({ error }) => (
  <motion.div
    className="mb-4"
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
  >
    <div className="p-3 sm:p-4 bg-red-50 text-red-500 rounded-2xl shadow-sm rounded-bl-none max-w-[90%] sm:max-w-[85%] text-sm sm:text-base">
      {error}
    </div>
  </motion.div>
);

const WelcomeMessage = () => (
  <div className="flex-1 flex flex-col items-center justify-center px-4 sm:px-6">
    <motion.div
      className="text-center"
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
    >
      <motion.h2
        className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-dexin-primary mb-2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        Chào mừng bạn đến với
      </motion.h2>
      <motion.h2
        className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-dexin-primary mb-6 sm:mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        Nara - Chuyên gia nội thất chữa lành
      </motion.h2>
    </motion.div>
    <motion.div
      className="w-full max-w-2xl px-2 sm:px-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.7 }}
    >
      <div className="p-3 sm:p-4 bg-white rounded-2xl shadow-sm rounded-bl-none">
        <div className="markdown-content prose prose-sm max-w-none text-sm sm:text-base">
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {WELCOME_CONTENT}
          </ReactMarkdown>
        </div>
      </div>
    </motion.div>
  </div>
);

const LimitReachedNotification = () => (
  <motion.div
    className="max-w-2xl mx-auto px-2 sm:px-0 mt-3"
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
  >
    <div className="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-2xl p-4">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
            <span className="text-red-600 text-lg">🚫</span>
          </div>
        </div>
        <div className="flex-1">
          <h3 className="text-sm font-semibold text-red-800 mb-1">
            Bạn đã hết lượt hỏi hôm nay!
          </h3>
          <p className="text-xs text-red-600 mb-3">
            Để được tư vấn chi tiết không giới hạn, hãy chat trực tiếp với nhân viên DEXIN.
          </p>
          <Link
            to="/ngo-loi"
            className="inline-flex items-center px-4 py-2 bg-dexin-primary text-white text-sm font-medium rounded-full hover:bg-dexin-primary/90 transition-colors"
          >
            💬 Chat với nhân viên
            <ArrowRight size={14} className="ml-1" />
          </Link>
        </div>
      </div>
    </div>
  </motion.div>
);

const MoLoi = () => {
  const { chatHistory, isLoading, error, sendMessage, messageCount, isLimitReached, messageLimit } = useChat();
  const [input, setInput] = useState('');
  const messagesEndRef = useRef(null);
  const [welcomeMessage, setWelcomeMessage] = useState(true);
  const [showSidebar, setShowSidebar] = useState(false);
  const [showImageUpload, setShowImageUpload] = useState(false);
  const [selectedImages, setSelectedImages] = useState([]);
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
  const isApiKeyValid = apiKey && apiKey !== 'your-api-key-here';

  // Tạo danh sách chat từ lịch sử tin nhắn
  const generateChatSamples = () => {
    const samples = [{ id: 'today-header', title: 'Hôm nay', isHeader: true }];

    const firstUserMessage = chatHistory.find(msg => msg.role === 'user');
    if (firstUserMessage) {
      const title = firstUserMessage.content.length > 25
        ? firstUserMessage.content.substring(0, 25) + '...'
        : firstUserMessage.content;
      samples.push({ id: 'current-chat', title, active: true });
    }

    return samples;
  };

  // Cuộn xuống tin nhắn mới nhất
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatHistory]);

  // Hàm gửi tin nhắn (hỗ trợ cả text và hình ảnh)
  const handleSendMessage = async () => {
    if (!input.trim() && selectedImages.length === 0) return;

    const messageText = input.trim();
    const imagesToSend = [...selectedImages];

    setInput('');
    setSelectedImages([]);
    setWelcomeMessage(false);

    await sendMessage(messageText, imagesToSend);
  };

  // Hàm xử lý khi chọn hình ảnh từ ImageUpload component
  const handleImageSelect = (images) => {
    setSelectedImages(images);
    setShowImageUpload(false);
  };

  // Hàm xóa hình ảnh đã chọn
  const removeSelectedImage = (index) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
  };

  // Xử lý khi nhấn Enter
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Toggle sidebar
  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  return (
    <div className="flex h-screen w-full overflow-hidden">
      {/* Mobile overlay */}
      <AnimatePresence>
        {showSidebar && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 sm:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={toggleSidebar}
          />
        )}
      </AnimatePresence>

      {/* Sidebar with animation - Responsive */}
      <AnimatePresence>
        {showSidebar && (
          <motion.div
            className="w-full sm:w-64 md:w-72 lg:w-80 flex-shrink-0 bg-dexin-light-20 flex flex-col h-full fixed sm:relative z-50 sm:z-auto"
            initial={{ x: -320 }}
            animate={{ x: 0 }}
            exit={{ x: -320 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            {/* Sidebar header - Responsive */}
            <div className="p-3 sm:p-4 flex items-center justify-between">
              <div className="flex items-center">
                <motion.button
                  onClick={toggleSidebar}
                  className="mr-2 p-1.5 sm:p-1 rounded-full hover:bg-pink-100 text-gray-600 hover:text-dexin-primary"
                  aria-label="Close sidebar"
                  whileTap={{ scale: 0.9 }}
                >
                  <ArrowLeft size={20} />
                </motion.button>
                <Link to="/" className="flex items-center">
                  <motion.img
                    src="/images/Logo Mini.png"
                    alt="DEXIN"
                    className="h-8 sm:h-10"
                    whileHover={{ scale: 1.05 }}
                  />
                </Link>
              </div>
              <div className="flex items-center space-x-2 sm:space-x-3">
                <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                  <Search size={20} className="sm:w-6 sm:h-6 text-gray-600 cursor-pointer hover:text-dexin-primary" />
                </motion.div>
                <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                  <PenSquare size={20} className="sm:w-6 sm:h-6 text-gray-600 cursor-pointer hover:text-dexin-primary" />
                </motion.div>
              </div>
            </div>

            {/* Message Counter in Sidebar */}
            <div className="px-3 sm:px-4 pb-3">
              <MessageCounter
                isLimitReached={isLimitReached}
                messageCount={messageCount}
                messageLimit={messageLimit}
                className="w-full text-center"
                isSidebar={true}
              />
            </div>

            {/* Chat history list with staggered animation - Responsive */}
            <motion.div
              className="flex-1 overflow-y-auto px-2 sm:px-2 pt-2"
              initial="hidden"
              animate="visible"
              variants={{
                hidden: { opacity: 0 },
                visible: {
                  opacity: 1,
                  transition: {
                    staggerChildren: 0.07
                  }
                }
              }}
            >
              {generateChatSamples().map(chat =>
                chat.isHeader ? (
                  <motion.div
                    key={chat.id}
                    className="px-3 sm:px-4 py-2 text-sm font-medium text-gray-500"
                    variants={{
                      hidden: { opacity: 0, x: -20 },
                      visible: { opacity: 1, x: 0 }
                    }}
                  >
                    {chat.title}
                  </motion.div>
                ) : (
                  <motion.div
                    key={chat.id}
                    className={`py-3 px-3 sm:px-4 rounded-xl cursor-pointer mb-1 ${
                      chat.active ? 'bg-dexin-light-50' : 'hover:bg-pink-50'
                    }`}
                    variants={{
                      hidden: { opacity: 0, x: -20 },
                      visible: { opacity: 1, x: 0 }
                    }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="text-sm font-medium truncate max-w-full">{chat.title}</div>
                  </motion.div>
                )
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main chat area - Responsive */}
      <div className="flex-1 flex flex-col h-full bg-dexin-bg">
        {/* Header with Logo when sidebar is hidden - Responsive */}
        {!showSidebar && (
          <motion.div
            className="p-3 sm:p-4 border-b flex items-center justify-between"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center">
              <motion.button
                onClick={toggleSidebar}
                className="mr-2 sm:mr-3 p-2 rounded-full hover:bg-gray-100"
                aria-label="Toggle sidebar"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Menu size={20} className="sm:w-6 sm:h-6 text-gray-600" />
              </motion.button>
              <Link to="/" className="flex items-center">
                <motion.img
                  src="/images/Logo Mini.png"
                  alt="DEXIN"
                  className="h-8 sm:h-10"
                  whileHover={{ scale: 1.05 }}
                />
              </Link>
            </div>

            {/* Message Counter */}
            <div className="flex items-center">
              <MessageCounter
                isLimitReached={isLimitReached}
                messageCount={messageCount}
                messageLimit={messageLimit}
              />
            </div>
          </motion.div>
        )}

        {/* Chat container */}
        <div className={`flex-1 flex flex-col overflow-hidden ${showSidebar ? '' : 'pt-0'}`}>
          {!isApiKeyValid && (
            <div className="p-6">
              {/* API key guide */}
            </div>
          )}

          {/* Centered welcome message - Mobile Responsive */}
          {isApiKeyValid && welcomeMessage && chatHistory.length === 0 && (
            <WelcomeMessage />
          )}

          {/* Centered chat messages - Mobile Responsive */}
          {(chatHistory.length > 0 || (!welcomeMessage && isApiKeyValid)) && (
            <div className="flex-1 overflow-y-auto py-4 sm:py-6 px-3 sm:px-4">
              <div className="max-w-2xl mx-auto">
                {isApiKeyValid && chatHistory.length === 0 && !welcomeMessage && !isLoading && (
                  <motion.div
                    className="mb-4 sm:mb-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="p-3 sm:p-4 bg-white rounded-2xl shadow-sm rounded-bl-none">
                    </div>
                  </motion.div>
                )}

                {chatHistory.map((message, index) => (
                  <motion.div
                    key={index}
                    className={`mb-4 sm:mb-6 ${message.role === 'user' ? 'flex justify-end' : 'flex justify-start'}`}
                    initial={{
                      opacity: 0,
                      y: 10
                    }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      duration: 0.3,
                      delay: index * 0.1
                    }}
                  >
                    <motion.div
                      className={`max-w-[90%] sm:max-w-[85%] ${
                        message.role === 'user'
                          ? 'bg-dexin-chat text-gray-800 rounded-2xl rounded-br-none ml-auto'
                          : 'bg-white border-2 border-dexin-light-20 shadow-sm rounded-2xl rounded-bl-none mr-auto'
                      } p-3 sm:p-4`}
                      whileHover={{ scale: 1.01 }}
                    >
                      {/* Hiển thị hình ảnh nếu có */}
                      {message.images && message.images.length > 0 && (
                        <div className="mb-3">
                          <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                            {message.images.map((image, imgIndex) => (
                              <div key={imgIndex} className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                                <img
                                  src={image.preview}
                                  alt={image.name}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Hiển thị nội dung text */}
                      {message.content && (
                        <div className={`markdown-content prose prose-sm max-w-none text-sm sm:text-base ${message.role === 'user' ? 'whitespace-pre-line' : ''}`}>
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            components={{
                              a: ({ href, children }) => {
                                // Xử lý link đặc biệt cho chat với nhân viên
                                if (href === '/ngo-loi') {
                                  return (
                                    <Link
                                      to="/ngo-loi"
                                      className="inline-flex items-center px-3 py-1 bg-dexin-primary text-white text-sm font-medium rounded-full hover:bg-dexin-primary/90 transition-colors no-underline"
                                    >
                                      {children}
                                      <ArrowRight size={12} className="ml-1" />
                                    </Link>
                                  );
                                }
                                // Link thường
                                return (
                                  <a href={href} className="text-dexin-primary hover:underline" target="_blank" rel="noopener noreferrer">
                                    {children}
                                  </a>
                                );
                              }
                            }}
                          >
                            {message.content}
                          </ReactMarkdown>
                        </div>
                      )}
                    </motion.div>
                  </motion.div>
                ))}

                {isLoading && <LoadingIndicator />}

                {error && !chatHistory.some(msg => msg.content === error) && (
                  <ErrorMessage error={error} />
                )}

                <div ref={messagesEndRef} />
              </div>
            </div>
          )}

          {/* Input box with animation - centered and mobile responsive */}
          <motion.div
            className="p-3 sm:p-4 bg-dexin-bg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="max-w-2xl mx-auto px-2 sm:px-0">
              {/* Hiển thị hình ảnh đã chọn */}
              {selectedImages.length > 0 && (
                <SelectedImages
                  selectedImages={selectedImages}
                  onRemoveImage={removeSelectedImage}
                  onClearAll={() => setSelectedImages([])}
                />
              )}

              <motion.div
                className="flex items-center bg-white border rounded-full p-2 shadow-sm border-2 border-dexin-light"
                whileHover={{ boxShadow: "0 4px 8px rgba(254, 124, 171, 0.25)" }}
                animate={{ boxShadow: "0 2px 4px rgba(254, 124, 171, 0.1)" }}
              >
                {/* Nút upload hình ảnh */}
                <motion.button
                  onClick={() => setShowImageUpload(true)}
                  disabled={!isApiKeyValid || isLimitReached}
                  className="p-2 rounded-full hover:bg-gray-100 text-gray-600 hover:text-dexin-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={{ scale: isLimitReached ? 1 : 1.1 }}
                  whileTap={{ scale: isLimitReached ? 1 : 0.95 }}
                  title={isLimitReached ? "Hết lượt hỏi" : "Đính kèm hình ảnh"}
                >
                  <ImageIcon size={18} className="sm:w-5 sm:h-5" />
                </motion.button>

                <textarea
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder={isLimitReached
                    ? "Bạn đã hết lượt hỏi. Chat với nhân viên để được tư vấn thêm!"
                    : "Hỏi Nara về... hoặc đính kèm hình ảnh không gian của bạn"
                  }
                  className="flex-grow px-3 sm:px-4 py-2 focus:outline-none resize-none text-sm sm:text-base disabled:bg-gray-100 disabled:text-gray-500"
                  disabled={!isApiKeyValid || isLimitReached}
                  rows={1}
                />
                <motion.button
                  onClick={handleSendMessage}
                  disabled={isLoading || (!input.trim() && selectedImages.length === 0) || !isApiKeyValid || isLimitReached}
                  className={`p-2 rounded-full min-w-[40px] min-h-[40px] flex items-center justify-center ${
                    (!input.trim() && selectedImages.length === 0) || isLoading || !isApiKeyValid || isLimitReached
                      ? 'text-gray-400'
                      : 'text-white bg-dexin-primary hover:bg-dexin-primary/90'
                  }`}
                  whileHover={{ scale: (!input.trim() && selectedImages.length === 0) || isLimitReached ? 1 : 1.1 }}
                  whileTap={{ scale: (!input.trim() && selectedImages.length === 0) || isLimitReached ? 1 : 0.95 }}
                >
                  {isLoading ? (
                    <div className="w-5 h-5 sm:w-6 sm:h-6 border-2 border-t-white border-white/20 rounded-full animate-spin"></div>
                  ) : (
                    <ArrowRight size={18} className="sm:w-5 sm:h-5" />
                  )}
                </motion.button>
              </motion.div>
            </div>

            {/* Thông báo khi hết lượt hỏi */}
            {isLimitReached && <LimitReachedNotification />}
          </motion.div>
        </div>
      </div>

      {/* Image Upload Modal */}
      <ImageUpload
        isOpen={showImageUpload}
        onClose={() => setShowImageUpload(false)}
        onImageSelect={handleImageSelect}
      />
    </div>
  );
};

export default MoLoi;