import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { useChat } from '../context/ChatContext';
import ImageUpload from '../components/chat/ImageUpload';

// Import components từ folder Moloi
import Sidebar from './Moloi/Sidebar';
import Header from './Moloi/Header';
import WelcomeMessage from './Moloi/WelcomeMessage';
import ChatMessage from './Moloi/ChatMessage';
import LoadingIndicator from './Moloi/LoadingIndicator';
import ErrorMessage from './Moloi/ErrorMessage';
import ChatInput from './Moloi/ChatInput';
import LimitReachedNotification from './Moloi/LimitReachedNotification';

const MoLoi = () => {
  const { chatHistory, isLoading, error, sendMessage, messageCount, isLimitReached, messageLimit } = useChat();
  const [input, setInput] = useState('');
  const messagesEndRef = useRef(null);
  const [welcomeMessage, setWelcomeMessage] = useState(true);
  const [showSidebar, setShowSidebar] = useState(false);
  const [showImageUpload, setShowImageUpload] = useState(false);
  const [selectedImages, setSelectedImages] = useState([]);
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
  const isApiKeyValid = apiKey && apiKey !== 'your-api-key-here';

  // Cuộn xuống tin nhắn mới nhất
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatHistory]);

  // Hàm gửi tin nhắn (hỗ trợ cả text và hình ảnh)
  const handleSendMessage = async () => {
    if (!input.trim() && selectedImages.length === 0) return;

    const messageText = input.trim();
    const imagesToSend = [...selectedImages];

    setInput('');
    setSelectedImages([]);
    setWelcomeMessage(false);

    await sendMessage(messageText, imagesToSend);
  };

  // Hàm xử lý khi chọn hình ảnh từ ImageUpload component
  const handleImageSelect = (images) => {
    setSelectedImages(images);
    setShowImageUpload(false);
  };

  // Hàm xóa hình ảnh đã chọn
  const removeSelectedImage = (index) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
  };

  // Xử lý khi nhấn Enter
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Toggle sidebar
  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  return (
    <div className="flex h-screen w-full overflow-hidden">
      {/* Mobile overlay */}
      <AnimatePresence>
        {showSidebar && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 sm:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={toggleSidebar}
          />
        )}
      </AnimatePresence>

      {/* Sidebar with animation - Responsive */}
      <AnimatePresence>
        {showSidebar && (
          <Sidebar 
            showSidebar={showSidebar}
            toggleSidebar={toggleSidebar}
            isLimitReached={isLimitReached}
            messageCount={messageCount}
            messageLimit={messageLimit}
            chatHistory={chatHistory}
          />
        )}
      </AnimatePresence>

      {/* Main chat area - Responsive */}
      <div className="flex-1 flex flex-col h-full bg-dexin-bg">
        {/* Header with Logo when sidebar is hidden - Responsive */}
        <Header 
          showSidebar={showSidebar}
          toggleSidebar={toggleSidebar}
          isLimitReached={isLimitReached}
          messageCount={messageCount}
          messageLimit={messageLimit}
        />

        {/* Chat container */}
        <div className={`flex-1 flex flex-col overflow-hidden ${showSidebar ? '' : 'pt-0'}`}>
          {!isApiKeyValid && (
            <div className="p-6">
              {/* API key guide */}
            </div>
          )}

          {/* Centered welcome message - Mobile Responsive */}
          {isApiKeyValid && welcomeMessage && chatHistory.length === 0 && (
            <WelcomeMessage />
          )}

          {/* Centered chat messages - Mobile Responsive */}
          {(chatHistory.length > 0 || (!welcomeMessage && isApiKeyValid)) && (
            <div className="flex-1 overflow-y-auto py-4 sm:py-6 px-3 sm:px-4">
              <div className="max-w-2xl mx-auto">
                {isApiKeyValid && chatHistory.length === 0 && !welcomeMessage && !isLoading && (
                  <motion.div
                    className="mb-4 sm:mb-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="p-3 sm:p-4 bg-white rounded-2xl shadow-sm rounded-bl-none">
                    </div>
                  </motion.div>
                )}

                {chatHistory.map((message, index) => (
                  <ChatMessage key={index} message={message} index={index} />
                ))}

                {isLoading && <LoadingIndicator />}

                {error && !chatHistory.some(msg => msg.content === error) && (
                  <ErrorMessage error={error} />
                )}

                <div ref={messagesEndRef} />
              </div>
            </div>
          )}

          {/* Input box with animation - centered and mobile responsive */}
          <ChatInput 
            input={input}
            setInput={setInput}
            selectedImages={selectedImages}
            setSelectedImages={setSelectedImages}
            removeSelectedImage={removeSelectedImage}
            handleSendMessage={handleSendMessage}
            handleKeyDown={handleKeyDown}
            setShowImageUpload={setShowImageUpload}
            isApiKeyValid={isApiKeyValid}
            isLimitReached={isLimitReached}
            isLoading={isLoading}
          />

          {/* Thông báo khi hết lượt hỏi */}
          {isLimitReached && <LimitReachedNotification />}
        </div>
      </div>

      {/* Image Upload Modal */}
      <ImageUpload
        isOpen={showImageUpload}
        onClose={() => setShowImageUpload(false)}
        onImageSelect={handleImageSelect}
      />
    </div>
  );
};

export default MoLoi;
