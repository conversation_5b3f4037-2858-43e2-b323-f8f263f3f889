import React from 'react';
import { motion } from 'motion/react';
import { ArrowRight, ImageIcon } from 'lucide-react';
import SelectedImages from './SelectedImages';

const ChatInput = ({
  input,
  setInput,
  selectedImages,
  setSelectedImages,
  removeSelectedImage,
  handleSendMessage,
  handleKeyDown,
  setShowImageUpload,
  isApiKeyValid,
  isLimitReached,
  isLoading
}) => {
  return (
    <motion.div
      className="p-3 sm:p-4 bg-dexin-bg"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="max-w-2xl mx-auto px-2 sm:px-0">
        {/* Hiển thị hình ảnh đã chọn */}
        {selectedImages.length > 0 && (
          <SelectedImages 
            selectedImages={selectedImages}
            onRemoveImage={removeSelectedImage}
            onClearAll={() => setSelectedImages([])}
          />
        )}

        <motion.div
          className="flex items-center bg-white border rounded-full p-2 shadow-sm border-2 border-dexin-light"
          whileHover={{ boxShadow: "0 4px 8px rgba(254, 124, 171, 0.25)" }}
          animate={{ boxShadow: "0 2px 4px rgba(254, 124, 171, 0.1)" }}
        >
          {/* Nút upload hình ảnh */}
          <motion.button
            onClick={() => setShowImageUpload(true)}
            disabled={!isApiKeyValid || isLimitReached}
            className="p-2 rounded-full hover:bg-gray-100 text-gray-600 hover:text-dexin-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            whileHover={{ scale: isLimitReached ? 1 : 1.1 }}
            whileTap={{ scale: isLimitReached ? 1 : 0.95 }}
            title={isLimitReached ? "Hết lượt hỏi" : "Đính kèm hình ảnh"}
          >
            <ImageIcon size={18} className="sm:w-5 sm:h-5" />
          </motion.button>

          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={isLimitReached
              ? "Bạn đã hết lượt hỏi. Chat với nhân viên để được tư vấn thêm!"
              : "Hỏi Nara về... hoặc đính kèm hình ảnh không gian của bạn"
            }
            className="flex-grow px-3 sm:px-4 py-2 focus:outline-none resize-none text-sm sm:text-base disabled:bg-gray-100 disabled:text-gray-500"
            disabled={!isApiKeyValid || isLimitReached}
            rows={1}
          />
          
          <motion.button
            onClick={handleSendMessage}
            disabled={isLoading || (!input.trim() && selectedImages.length === 0) || !isApiKeyValid || isLimitReached}
            className={`p-2 rounded-full min-w-[40px] min-h-[40px] flex items-center justify-center ${
              (!input.trim() && selectedImages.length === 0) || isLoading || !isApiKeyValid || isLimitReached
                ? 'text-gray-400'
                : 'text-white bg-dexin-primary hover:bg-dexin-primary/90'
            }`}
            whileHover={{ scale: (!input.trim() && selectedImages.length === 0) || isLimitReached ? 1 : 1.1 }}
            whileTap={{ scale: (!input.trim() && selectedImages.length === 0) || isLimitReached ? 1 : 0.95 }}
          >
            {isLoading ? (
              <div className="w-5 h-5 sm:w-6 sm:h-6 border-2 border-t-white border-white/20 rounded-full animate-spin"></div>
            ) : (
              <ArrowRight size={18} className="sm:w-5 sm:h-5" />
            )}
          </motion.button>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default ChatInput;
