import React from 'react';
import { motion } from 'motion/react';
import { ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const ChatMessage = ({ message, index }) => {
  return (
    <motion.div
      className={`mb-4 sm:mb-6 ${message.role === 'user' ? 'flex justify-end' : 'flex justify-start'}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
    >
      <motion.div
        className={`max-w-[90%] sm:max-w-[85%] ${
          message.role === 'user'
            ? 'bg-dexin-chat text-gray-800 rounded-2xl rounded-br-none ml-auto'
            : 'bg-white border-2 border-dexin-light-20 shadow-sm rounded-2xl rounded-bl-none mr-auto'
        } p-3 sm:p-4`}
        whileHover={{ scale: 1.01 }}
      >
        {/* <PERSON><PERSON><PERSON> thị hình ảnh nếu có */}
        {message.images && message.images.length > 0 && (
          <div className="mb-3">
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              {message.images.map((image, imgIndex) => (
                <div key={imgIndex} className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                  <img
                    src={image.preview}
                    alt={image.name}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Hiển thị nội dung text */}
        {message.content && (
          <div className={`markdown-content prose prose-sm max-w-none text-sm sm:text-base ${message.role === 'user' ? 'whitespace-pre-line' : ''}`}>
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                a: ({ href, children }) => {
                  // Xử lý link đặc biệt cho chat với nhân viên
                  if (href === '/ngo-loi') {
                    return (
                      <Link
                        to="/ngo-loi"
                        className="inline-flex items-center px-3 py-1 bg-dexin-primary text-white text-sm font-medium rounded-full hover:bg-dexin-primary/90 transition-colors no-underline"
                      >
                        {children}
                        <ArrowRight size={12} className="ml-1" />
                      </Link>
                    );
                  }
                  // Link thường
                  return (
                    <a href={href} className="text-dexin-primary hover:underline" target="_blank" rel="noopener noreferrer">
                      {children}
                    </a>
                  );
                }
              }}
            >
              {message.content}
            </ReactMarkdown>
          </div>
        )}
      </motion.div>
    </motion.div>
  );
};

export default ChatMessage;
