import React from 'react';
import { motion } from 'motion/react';

const ErrorMessage = ({ error }) => (
  <motion.div
    className="mb-4"
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
  >
    <div className="p-3 sm:p-4 bg-red-50 text-red-500 rounded-2xl shadow-sm rounded-bl-none max-w-[90%] sm:max-w-[85%] text-sm sm:text-base">
      {error}
    </div>
  </motion.div>
);

export default ErrorMessage;
