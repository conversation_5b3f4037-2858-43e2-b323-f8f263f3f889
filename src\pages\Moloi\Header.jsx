import React from 'react';
import { motion } from 'motion/react';
import { Menu } from 'lucide-react';
import { Link } from 'react-router-dom';
import MessageCounter from './MessageCounter';

const Header = ({ 
  showSidebar, 
  toggleSidebar, 
  isLimitReached, 
  messageCount, 
  messageLimit 
}) => {
  if (showSidebar) return null;

  return (
    <motion.div
      className="p-3 sm:p-4 border-b flex items-center justify-between"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center">
        <motion.button
          onClick={toggleSidebar}
          className="mr-2 sm:mr-3 p-2 rounded-full hover:bg-gray-100"
          aria-label="Toggle sidebar"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <Menu size={20} className="sm:w-6 sm:h-6 text-gray-600" />
        </motion.button>
        <Link to="/" className="flex items-center">
          <motion.img
            src="/images/Logo Mini.png"
            alt="DEXIN"
            className="h-8 sm:h-10"
            whileHover={{ scale: 1.05 }}
          />
        </Link>
      </div>

      {/* Message Counter */}
      <div className="flex items-center">
        <MessageCounter 
          isLimitReached={isLimitReached}
          messageCount={messageCount}
          messageLimit={messageLimit}
        />
      </div>
    </motion.div>
  );
};

export default Header;
