import React from 'react';
import { motion } from 'motion/react';
import { getCounterStyle, getCounterText } from './utils';

const MessageCounter = ({ isLimitReached, messageCount, messageLimit, className = "", isSidebar = false }) => (
  <motion.div
    className={`px-3 py-1 rounded-full text-xs sm:text-sm font-medium ${getCounterStyle(isLimitReached, messageCount, messageLimit)} ${className}`}
    initial={{ opacity: 0, scale: 0.8 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.3 }}
  >
    {getCounterText(isLimitReached, messageCount, messageLimit, isSidebar)}
  </motion.div>
);

export default MessageCounter;
