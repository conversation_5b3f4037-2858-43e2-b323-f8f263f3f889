import React from 'react';
import { motion } from 'motion/react';
import { ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

const LimitReachedNotification = () => (
  <motion.div
    className="max-w-2xl mx-auto px-2 sm:px-0 mt-3"
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
  >
    <div className="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-2xl p-4">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
            <span className="text-red-600 text-lg">🚫</span>
          </div>
        </div>
        <div className="flex-1">
          <h3 className="text-sm font-semibold text-red-800 mb-1">
            <PERSON><PERSON><PERSON> đã hết lượt hỏi hôm nay!
          </h3>
          <p className="text-xs text-red-600 mb-3">
            <PERSON><PERSON> được tư vấn chi tiết không giới hạn, hãy chat trực tiếp với nhân viên DEXIN.
          </p>
          <Link
            to="/ngo-loi"
            className="inline-flex items-center px-4 py-2 bg-dexin-primary text-white text-sm font-medium rounded-full hover:bg-dexin-primary/90 transition-colors"
          >
            💬 Chat với nhân viên
            <ArrowRight size={14} className="ml-1" />
          </Link>
        </div>
      </div>
    </div>
  </motion.div>
);

export default LimitReachedNotification;
