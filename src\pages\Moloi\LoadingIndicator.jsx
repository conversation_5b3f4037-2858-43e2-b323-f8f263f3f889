import React from 'react';
import { motion } from 'motion/react';

const LoadingIndicator = () => (
  <motion.div
    className="mb-4"
    initial={{ opacity: 0, scale: 0.8 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.3 }}
  >
    <div className="p-3 sm:p-4 bg-dexin-light-20 rounded-2xl shadow-sm rounded-bl-none max-w-[90%] sm:max-w-[85%]">
      <div className="flex space-x-2">
        <div className="w-2 h-2 rounded-full bg-white animate-bounce"></div>
        <div className="w-2 h-2 rounded-full bg-white animate-bounce delay-75"></div>
        <div className="w-2 h-2 rounded-full bg-white animate-bounce delay-150"></div>
      </div>
    </div>
  </motion.div>
);

export default LoadingIndicator;
