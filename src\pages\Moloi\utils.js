// Helper functions cho <PERSON><PERSON><PERSON>
export const getCounterStyle = (isLimitReached, messageCount, messageLimit) => {
  if (isLimitReached) return 'bg-red-100 text-red-600';
  if (messageCount >= messageLimit - 3) return 'bg-yellow-100 text-yellow-600';
  return 'bg-green-100 text-green-600';
};

export const getCounterText = (isLimitReached, messageCount, messageLimit, isSidebar = false) => {
  if (isLimitReached) return isSidebar ? '🚫 Hết lượt hỏi hôm nay' : '🚫 Hết lượt hỏi';
  const suffix = isSidebar ? ' lượt hỏi' : ' lượt';
  return `💬 Còn ${messageLimit - messageCount}/${messageLimit}${suffix}`;
};

// Tạo danh sách chat từ lịch sử tin nhắn
export const generateChatSamples = (chatHistory) => {
  const samples = [{ id: 'today-header', title: '<PERSON>ôm nay', isHeader: true }];
  
  const firstUserMessage = chatHistory.find(msg => msg.role === 'user');
  if (firstUserMessage) {
    const title = firstUserMessage.content.length > 25 
      ? firstUserMessage.content.substring(0, 25) + '...'
      : firstUserMessage.content;
    samples.push({ id: 'current-chat', title, active: true });
  }

  return samples;
};
